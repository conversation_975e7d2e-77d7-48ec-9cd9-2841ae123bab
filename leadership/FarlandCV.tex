%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Jonathan Farland's CV
% November 25, 2023
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

%----------------------------------------------------------------------------------------
%	PACKAGES AND OTHER DOCUMENT CONFIGURATIONS
%----------------------------------------------------------------------------------------

\documentclass{resume} % Use the custom resume.cls style

\usepackage[left=0.5in,top=0.6in,right=0.5in,bottom=0.6in]{geometry} % Document margins
\usepackage{hyperref}

\name{Jon <PERSON>} % Your name
\address{San Francisco Bay Area \\ (508)~$\cdot$~237~$\cdot$~8192} % Your address
\address{<EMAIL> \\ \href{https://www.linkedin.com/in/jonathan-farland-40096216}{LinkedIn} \\ \href{https://github.com/jfarland}{github}}
\address{
	\href{https://www.youtube.com/watch?v=NlwiA0BAVBw}{\bf AI Builders Series},
	\href{https://www.youtube.com/watch?v=N0gyDVUFPlg}{\bf Foundational Models for Time Series},
	\href{https://github.com/h2oai/h2o_genai_training}{\bf Generative AI Training},
	\href{https://www.youtube.com/watch?v=nYbqJDCZ2wI}{\bf Silicon Angle}}
	%\href{https://www.youtube.com/watch?v=cTOPfsjMflE&feature=youtu.be}{Spark Summit}}
\begin{document}

%----------------------------------------------------------------------------------------
%	WORK EXPERIENCE SECTION
%----------------------------------------------------------------------------------------

\begin{rSection}{Experience}
\begin{rSubsection}{H2O.ai}{Dec 2021 to Present}{Director of AI Solutions Engineering}{San Francisco Bay Area, CA}
\begin{itemize}
\item {\bf Leadership}
	\begin{itemize}
	\item Led a team of data scientists and engineers to develop AI solutions.
	\item Spearheaded multiple AI projects to improve operational efficiency.
	\end{itemize}
\item {\bf Technical Contributions}
	\begin{itemize}
	\item Developed machine learning models for predictive analytics.
	\item Implemented scalable AI solutions using cloud technologies.
	\end{itemize}
\end{itemize}
\end{rSubsection}

\begin{rSubsection}{TROVE Predictive Data Science (Acquired by E Source)}{Oct 2017 to Dec 2021}{Senior Data Scientist, Manager}{San Francisco Bay Area, CA}
\begin{itemize}
\item Developed operational AI Models still used in production across North American Utilities
\item Led a team of data scientists and software engineers to deploy large scale, real-time predictive modeling systems
\end{itemize}
\end{rSubsection}

%------------------------------------------------

\begin{rSubsection}{KEMA Consulting (Acquired by DNV Energy)}{Aug 2012 - Oct 2017}{Senior Technical Consultant, Data Scientist, Project Manager}{Boston, MA}

\begin{itemize}
\item Proposed and managed technical studies related to the evaluation of energy programs both across the US and abroad. These include demand response, behavioral programs, distributed generation, renewables, and electric vehicle  penetration.
\item Responsible for stakeholder reporting and advisory services as lead technical consultant for the  predictive analytics team.
\item Developed hierarchical energy forecasting approaches to address growth in emerging technologies using machine learning and statistical techniques.
\end{itemize}
\end{rSubsection}

%------------------------------------------------

% \begin{rSubsection}{Independent System Operator New England}{Dec 2009 - Oct 2010}{Resource Adequacy}{Holyoke, MA}

% \item Designed heuristic algorithms that calculate dispatchable (real-time) availability of resources during system peak using empirical distributional fitting that employ nonparametric tests such as Kolmogorov-Smirnof and Jacques-Berra.
% \item Algorithms implemented using an VBA-enabled Excel front end using MATLAB as the analytical engine and a production Oracle server as the back-end.

% \end{rSubsection}

%------------------------------------------------

% \begin{rSubsection}{Department of Resource Economics}{Jan 2008 - Apr 2010}{Graduate Statistics Instructor}{Amherst, MA}
% \item Lecture, lab, and discussion of topics such as hypothesis testing, ANOVA, Multivariate Regression, Forecasting and Nonparametric Regression.
% %\item Management of an eight-member group of undergraduate teaching assistants to instruct a large scale intermediate statistics course.
% \end{rSubsection}
%
\end{rSection}

%----------------------------------------------------------------------------------------
%	EDUCATION SECTION
%----------------------------------------------------------------------------------------

\begin{rSection}{Education}

{\bf University of Massachussetts, Amherst, USA} \hfill {\em Aug 2012} \\
Masters of Science -  Applied Econometrics: Deans List, Cum Laude \\
Bachelors of Business Admin - Operations Research \& Finance \\
Minor in Resource Economics \\

{\bf University of Naples Federico, Portici, Italy} \hfill {\em Aug 2011} \\
Certificate of Course Completion - Advanced Micro-Econometrics \\

\end{rSection}

%----------------------------------------------------------------------------------------
%	TECHNICAL STRENGTHS SECTION
%----------------------------------------------------------------------------------------

\begin{rSection}{Technical Skills}

\begin{tabular}{ @{} >{\bfseries}l @{\hspace{6ex}} l }
Generative AI & HuggingFace, Langchain, NVIDIA NEMO, AI21, Enterprise h2oGPT, AI21, TimeGPT \\
Languages & Python, R, SQL, SAS, Matlab, Mathematica, VBA, AMPL  \\
Computing & Unix-based Systems, Spark, Docker, K8S, AWS, GCP, Azure, Databricks\\
Database & Postgresql, MySQL, MongoDB, Cassandra, DBeaver, QuestDB \\
Top Python Packages & pytorch, pandas, numpy, sklearn, sktime, tensorflow    \\
Top R Packages &  xgboost, shiny, caret, neuralnet, hts, h2o, sparklyr  \\
% Applications & Tableau, Advanced Microsoft Office \\
\end{tabular}

\end{rSection}

\pagebreak

%----------------------------------------------------------------------------------------
%	SELECTED PROJECT WORK
%----------------------------------------------------------------------------------------

\begin{rSection}{Selected Project Work}

\begin{rSubsection}{Generative AI for Regulatory Compliance in Banking}{}{Technical Advisor}{McLean, Virginia}
\item[] Led the development of an application that scraped public regulatory websites and persisted securely in a VectorDB 
\item[] Custom AI application built to automatically detect banking violations and generate a downloadable report
\item[] Achieved near real-time compliance scoring and violation detection in North America banking industry 
\end{rSubsection}

\begin{rSubsection}{AI for Genomics Pilot}{}{Senior Data Scientst}{Boston, Massachusetts}
\item[] Built a classifier to correctly predict cancer diagnosis using genomics data with 50k features
\item[] Improved True Positive Rate by about 30 percent by preprocessing genomics data with autoencoder and postprocessing with a metalearner
\end{rSubsection}

\begin{rSubsection}{Enterprise GenAI Deployment for Secure Installs}{}{Technical Advisor}{San Francisco, California}
\item[] Led the solution architecture team responsible for bundling enterprise GenAI technology for on-premise and even airgapped scenarios.
\item[] Built a deployment framework flexible enough to accommodate various security and privary requirements in federal government and public sector
\end{rSubsection}

\begin{rSubsection}{Predictive Analytics for Utility DR Operations}{}{Project Manager}{San Francisco, California}
\item[] Managed day to day client interactions and support while also tracking and developing the longer term product roadmap.
\item[] Designed and built a large-scale Demand Response forecasting system across all 7+ million meters of the Pacific Gas and Electric service territory and across the demand response portfolio, including residential, commercial and industrial sectors. These programs cover various designs ranging from behavioural to direct load control and dispatch.
\item[] Research and Development of novel hierarchal load impact forecasting algorithms using machine learning and statistical models.
\end{rSubsection}

\begin{rSubsection}{Advanced Electric Distribution Grid Forecasting}{}{Data Scientist}{San Francisco, California}
\item[] Developed advanced forecasting system to provide short-term hourly forecasts of energy demand across two large utility operating areas in California. Forecasts accounted for emerging technologies as well distributed generation such as solar and storage.
\item[] Smart meter and other telemetry data (e.g., SCADA) were paired with weather data and ingested into our propietary data model on an ongoing basis across millions of sensors in near real-time.
\item[] Linking down-stream sensors across the complex electrical distribution network enabled the software to forecast by aggregation for grid nodes that did not have any historical telemetry. This provided significant cost savings to the customer from not having to install expensive sensors across all grid nodes.
\end{rSubsection}

\begin{rSubsection}{Transformer Asset Risk Modeling }{}{Data Scientist}{Kansas City, Kansas}
\item[] Worked with in-house subject matter experts to codify institutional knowledge and include as a compliment to machine learning and AI-based predictive models
\item[] Used data from Dissolved Gas Analysis (DGA) testing  to predict the risk of various failure modes as well as the anticipated time-to-failure across electrical transformers.
\item[] Time series of key gases such as Acetylene, Hydrogen, Ethane, Methane, Ethylene and Nitrogen were used to classify transformers based on their perceived risk level.
\end{rSubsection}

\begin{rSubsection}{Real-time Customer Baseline and Financial Settlement Modeling}{}{Data Scientist}{Portland, Oregon}
\item[] Consulted for a medium sized utility company in the Pacific Northwest to launch their first
Peak Time Rebates program across the service territory.
\item[] Counterfactual customer-level baselines were generated for each customer using an ensemble of approaches including similar-day, regression, control groups and gradient boosting machine approaches.
\item[] Method selection was the result of an optimization model based on performance data in the most recent cross-validation period.
\end{rSubsection}

\begin{rSubsection}{Hierarchical Forecasting of Energy and Peak Demand in the Kingdom of Saudi Arabia}{}{Senior Data Scientist}{Riyadh, Saudia Arabia}
\item[] Data analytics and modeling for the largest end-use metering project in the world. Developed and delivered a three-day course on predictive analytics to subsequently train analytical staff at client site in Riyadh, Saudi Arabia. Seminar participants included staff from Saudi Aramco, Saudi Electricity Company, and the Electricity and Cogeneration Regulatory Authority.

\end{rSubsection}

% \pagebreak

\begin{rSubsection}{Global Energy Transition Outlook}{}{Technical Advisor}{Oslo, Norway}
\item[] Technical advisor for DNV GL's Energy Transition Outlook (ETO). This annual report seeks to identify and measure the major industry implications of the ongoing global energy transition for each of the OECD's regions. Developed bottom-up and top-down predictions of energy demand for each region of the globe until 2040.
\end{rSubsection}

\begin{rSubsection}{Caltrack Beta Test}{}{Lead Data Scientist}{San Francisco, California}
\item[] Primary code base developer for rapid measurement of site-level, weather normalized energy savings. Process and predictive results benchmarked across open-source implementations from \href{https://github.com/impactlab/caltrack}{Open EE Meter}. Algorithms implemented using R, Python and the Spark distributed computing framework on compute-optimized instances in the Amazon cloud.
\end{rSubsection}

\begin{rSubsection}{Behavioural Demand Response Evaluation}{}{Project Manager}{Ottawa, Canada}
\item[] Project Manager and Lead Data Scienstist for an impact evaluation pertaining to a hybrid energy program targeting both ongoing behavioural impacts as well as event-based hourly demand reductions. Analytics and reporting were generated using the Spark (1.5) distributed computing framework, Amazon Web Service S3 and EC2 instances, and the Databricks browser based platform.

\end{rSubsection}

% \begin{rSubsection}{Home Energy Reports Behavioural Evaluation}{}{Project Manager}{Seattle, Washington}
% \item[] Project Manager and Lead Data Scientist for the impact evaluation of client's Home Electricity Reports program, which used the Opower platform. The program was deployed with multiple overlapping randomized controlled trial experimental designs and a central part of the evaluation was identifying the appropriate way to estimate savings for all of the pieces.   Industry standard techniques for HER program evaluation were implemented. complete the impact evaluation.
% \end{rSubsection}

% \begin{rSubsection}{Critical Peak Pricing Pilot Evaluation}{}{Lead Data Scientist}{Portland, Oregon}
% \item[] Lead Data Scientist for an impact evaluation of clients's Critical Peak Pricing Pilot. Hourly regression models were estimated using Advanced Metering Infrastructure (AMI) data and NOAA weather data. These models were transferred to an excel-based tool capable of estimating and visualizing impacts under different weather scenarios.
% \end{rSubsection}

% \begin{rSubsection}{Dynamic Pricing Pilot Evaluation}{}{Lead Data Scientist}{State of Virginia}
% \item[] Lead Data Scientist for an impact and process evaluation of client's Dynamic Pricing Pilot. This pilot provides time of use rates to residential and commercial customers as a price signal for them to reduce consumption during system peak hours. The evaluation report includes a load impact analysis, surveys to assess awareness, understanding, and acceptance of rate, and reporting for both residential and commercial participants. Residential participants have a matched control group; there are no matched controls for commercial participants.
% \end{rSubsection}

\begin{rSubsection}{Electric Vehicle Pilot Evaluation}{}{Lead Data Scientist}{State of Virginia}
\item[] Lead Data Scientist for a pilot involving time-of-use charging rates for electric vehicle owner's in Virginia. The project focused on whole house impacts for EV owners, as well as vehicle-charging only impacts. Statistical methods were used to enumerate differences between control and treatment average load shapes for the vehicle-charging only members. A synthetic control group was generated for the whole house treatment members.
\end{rSubsection}

\begin{rSubsection}{Macroeconomic Modeling of State Commercial and Industrial Energy Sectors}{}{Lead Data Scientist}{State of Massachusetts}
\item[] Lead Data Scientist for a macroeconomic consumption modeling project of Massachusetts' Commercial and Industrial sectors. Our team developed a database of billing data pertaining to commercial and industrial premises for all energy efficiency program administrators in Massachusetts for three years. Along with NOAA weather data and economic data from the US Census Bureau and other sources, macroeconomic consumption models were used to estimate the impact of energy efficiency programs at the county and town level.
\end{rSubsection}

\begin{rSubsection}{Mathematical Programming Approach Towards Risk Mitigation in Sports Betting}{}{Graduate Researcher}{University of Massachusetts}
\item[] Developed a modeling technique to maximize profit subject to a zero probability of loss from sports betting. Solved using the Simplex Algorithm, with the CPLEX solver in AMPL.
\end{rSubsection}

\end{rSection}

\pagebreak

%----------------------------------------------------------------------------------------
%	PUBLICATIONS
%----------------------------------------------------------------------------------------

\begin{rSection}{Publications and Relevant Presentations}

\item {\bf Utility Load Research: The Future of Load Research is Now}, C. Puckett, C. Williamson, C. Godin, W. Gifford, J. Farland, T. Laing, T. Hong, IEEE Power and Energy Magazine, May/June, 2020

\item {\bf Model Based Matching and Other Benefits of High Frequency Interval Data}, L. Getachew, J. Farland, K. Agnew, P. Franzese, V. Richardson, G. Sadhasivan, International Energy Program Evaluation Conference, Baltimore, USA, 2017

\item {\bf Electricity End Use Forecasting Using Non-Intrusive Load Metering Technology}, J. Farland, C. Puckett, F. Coito, International Symposium on Forecasting, Cairns, Australia, 2017

\item {\bf High Resolution Energy Modeling that Scales with Apache Spark 2.0}, J. Farland, Spark Summit Boston, USA, 2017

\item {\bf Load Forecasing with Distributed Energy Resources}, J.Farland, F. Farzan, R.J. Hyndman, International Symposium on Forecasting, Santander, Spain, 2016

\item {\bf Breaking Down Analytical and Computational Barriers in Energy Data Analytics}, J. Farland, Spark Summit San Francisco, USA, 2016

\item {\bf  Zonal and Regional Load Forecasting in The New England Wholesale Electricity Market}: Semiparametric Regression Approach, Masters Thesis, University of Massachusetts, 2012

\end{rSection}

%----------------------------------------------------------------------------------------
%	HONORS SECTION
%----------------------------------------------------------------------------------------

\begin{rSection}{Professional Organizations and Distinctions}

\item Board of Directors, Peak Load Management Alliance, 2020-2021
\item Member, International Institute of Forecasting
\item Invited Speaker, 41st International Symposium on Forecasing, Charlottesville, VA, 2023
\item Speaker, 39th International Symposium on Forecasting, Thessaloniki, Greece, 2019
\item Invited Speaker, 38th International Symposium on Forecasting, Boulder, Colorado, 2018
\item Speaker, 37th International Symposium on Forecasting, Cairns, Australia, 2017
\item Speaker, Spark Summit East, Boston, USA, 2017
\item Session Chair, 36th International Symposium on Forecasting, Santander, Spain, 2016
\item Speaker, AEIC Advanced Load Research Applications, Nashville, Tennessee, USA, 2016
\item Speaker, Spark Advisory Forum, San Francisco, USA, 2016
\item Speaker, Spark Summit West, San Francisco, USA, 2016
\item Speaker, 34th International Symposium on Forecasting, Rotterdam, Netherlands, 2014
\item Vijay Bhagavan Distinquished Teaching Award, University of Massachusetts Amherst, 2012
\item International Advanced Econometrics Scholarship from Italian Ministry of Agriculture, 2011

\end{rSection}
%----------------------------------------------------------------------------------------

\end{document}

