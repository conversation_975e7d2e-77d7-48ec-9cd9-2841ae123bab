%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Medium Length Professional CV
% LaTeX Template
% Version 2.0 (8/5/13)
%
% This template has been downloaded from:
% http://www.LaTeXTemplates.com
%
% Original author:
% <PERSON> (http://www.treyhunner.com/)
%
% Important note:
% This template requires the resume.cls file to be in the same directory as the
% .tex file. The resume.cls file provides the resume style used for structuring the
% document.
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

%----------------------------------------------------------------------------------------
%	PACKAGES AND OTHER DOCUMENT CONFIGURATIONS
%----------------------------------------------------------------------------------------

\documentclass{resume} % Use the custom resume.cls style

\usepackage[left=0.75in,top=0.6in,right=0.75in,bottom=0.6in]{geometry} % Document margins

\name{Jonathan T Farland} % Your name
\address{389 Palm Avenue APT 17 \\ Oakland, California 94610} % Your address
%\address{123 Pleasant Lane \\ City, State 12345} % Your secondary addess (optional)
\address{(508)~$\cdot$~237~$\cdot$~8192 \\ <EMAIL>} % Your phone number and email

\begin{document}

%----------------------------------------------------------------------------------------
%	EDUCATION SECTION
%----------------------------------------------------------------------------------------

\begin{rSection}{Education}


{\bf University of Massachussetts, Amherst, USA} \hfill {\em August 2012} \\
M.Sc. in Applied Econometrics - Overall GPA: 3.73 \\
B.B.A. in Operations Research \& Finance \\
Minor in Resource Economics \\

{\bf University of Naples Federico, Portici, Italy} \hfill {\em August 2011} \\
Certificate of Course Completion - Advanced Micro-Econometrics \\

\end{rSection}

%----------------------------------------------------------------------------------------
%	WORK EXPERIENCE SECTION
%----------------------------------------------------------------------------------------

\begin{rSection}{Experience}

\begin{rSubsection}{DNV GL Energy}{August 2012 - Present}{Senior Data Scientist}{San Francisco Bay Area, CA}
\item Proposed and managed technical studies related to the evaluation of energy efficiency programs across the United States and abroad. These include demand response, behavioral programs, distributed generation, renewables, and electric vehicle  penetration. 
\item Responsible for client facing dissemination, reporting, and explanation of results and methods. 
\item Engineered and implemented machine and statistical learning procedures to fit both explanatory and predictive models
\end{rSubsection}

%------------------------------------------------

\begin{rSubsection}{ISO New England}{December 2009 - October 2010}{Resource Adequacy}{Holyoke, MA}
\item Researched and developed advanced electrical load forecasting methodologies
\item Curabitur dapibus enim sit amet elit pharetra tincidunt website feugiat nisl imperdiet. Ut convallis AJAX libero in urna ultrices accumsan.
\item Cum sociis natoque penatibus et magnis dis MySQL parturient montes, nascetur ridiculus mus.
\item In rutrum accumsan ultricies. Mauris vitae nisi at sem facilisis semper ac in est.
\item Nullam cursus suscipit nisi, et ultrices justo sodales nec. Fusce venenatis facilisis lectus ac semper.
\end{rSubsection}

%------------------------------------------------

\begin{rSubsection}{Department of Resource Economics}{January 2008 - April 2010}{Graduate Statistics Instructor}{Amherst, MA}
\item 
\item Curabitur venenatis pulvinar tellus gravida ornare. Sed et erat faucibus nunc euismod ultricies ut id
\end{rSubsection}

\end{rSection}

%----------------------------------------------------------------------------------------
%	TECHNICAL STRENGTHS SECTION
%----------------------------------------------------------------------------------------

\begin{rSection}{Technical Strengths}

\begin{tabular}{ @{} >{\bfseries}l @{\hspace{6ex}} l }
Languages & R, Python, SAS, Matlab, SQL, git, mercurial  \\
Computing & Spark (1.5) and Hadoop Distributed Computing Frameworks, AWS, Digital Ocean, Unix-systems \\
Tools & Vim, Databricks Platform, Ipython, Anaconda, Rstudio
\end{tabular}

\end{rSection}

%----------------------------------------------------------------------------------------
%	EXAMPLE SECTION
%----------------------------------------------------------------------------------------

%\begin{rSection}{Section Name}

%Section content\ldots

%\end{rSection}

%----------------------------------------------------------------------------------------

\end{document}
