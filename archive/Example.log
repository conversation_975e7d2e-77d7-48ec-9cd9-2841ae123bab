This is pdfTeX, Version 3.14159265-2.6-1.40.16 (MiKTeX 2.9) (preloaded format=pdflatex 2015.12.29)  30 DEC 2015 10:15
entering extended mode
**./Example.tex
(Example.tex
LaTeX2e <2015/10/01> patch level 2
Babel <3.9m> and hyphenation patterns for 69 languages loaded.
(resume.cls
Document Class: resume 2010/07/10 v0.9 Resume class
("C:\Users\<USER>\AppData\Local\Programs\MiKTeX 2.9\tex\latex\base\article.cls"
Document Class: article 2014/09/29 v1.4h Standard LaTeX document class
("C:\Users\<USER>\AppData\Local\Programs\MiKTeX 2.9\tex\latex\base\size11.clo"
File: size11.clo 2014/09/29 v1.4h Standard LaTeX file (size option)
)
\c@part=\count79
\c@section=\count80
\c@subsection=\count81
\c@subsubsection=\count82
\c@paragraph=\count83
\c@subparagraph=\count84
\c@figure=\count85
\c@table=\count86
\abovecaptionskip=\skip41
\belowcaptionskip=\skip42
\bibindent=\dimen102
)
("C:\Users\<USER>\AppData\Local\Programs\MiKTeX 2.9\tex\latex\ltxmisc\parskip.s
ty"
Package: parskip 2001/04/09 non-zero parskip adjustments
)
("C:\Users\<USER>\AppData\Local\Programs\MiKTeX 2.9\tex\latex\tools\array.sty"
Package: array 2014/10/28 v2.4c Tabular extension package (FMi)
\col@sep=\dimen103
\extrarowheight=\dimen104
\NC@list=\toks14
\extratabsurround=\skip43
\backup@length=\skip44
) ("C:\Users\<USER>\AppData\Local\Programs\MiKTeX 2.9\tex\latex\base\ifthen.sty
"
Package: ifthen 2014/09/29 v1.1c Standard LaTeX ifthen package (DPC)
))
("C:\Users\<USER>\AppData\Local\Programs\MiKTeX 2.9\tex\latex\geometry\geometry
.sty"
Package: geometry 2010/09/12 v5.6 Page Geometry

("C:\Users\<USER>\AppData\Local\Programs\MiKTeX 2.9\tex\latex\graphics\keyval.s
ty"
Package: keyval 2014/10/28 v1.15 key=value parser (DPC)
\KV@toks@=\toks15
)
("C:\Users\<USER>\AppData\Local\Programs\MiKTeX 2.9\tex\generic\oberdiek\ifpdf.
sty"
Package: ifpdf 2011/01/30 v2.3 Provides the ifpdf switch (HO)
Package ifpdf Info: pdfTeX in PDF mode is detected.
)
("C:\Users\<USER>\AppData\Local\Programs\MiKTeX 2.9\tex\generic\oberdiek\ifvtex
.sty"
Package: ifvtex 2010/03/01 v1.5 Detect VTeX and its facilities (HO)
Package ifvtex Info: VTeX not detected.
)
("C:\Users\<USER>\AppData\Local\Programs\MiKTeX 2.9\tex\generic\ifxetex\ifxetex
.sty"
Package: ifxetex 2010/09/12 v0.6 Provides ifxetex conditional
)
\Gm@cnth=\count87
\Gm@cntv=\count88
\c@Gm@tempcnt=\count89
\Gm@bindingoffset=\dimen105
\Gm@wd@mp=\dimen106
\Gm@odd@mp=\dimen107
\Gm@even@mp=\dimen108
\Gm@layoutwidth=\dimen109
\Gm@layoutheight=\dimen110
\Gm@layouthoffset=\dimen111
\Gm@layoutvoffset=\dimen112
\Gm@dimlist=\toks16

("C:\Users\<USER>\AppData\Local\Programs\MiKTeX 2.9\tex\latex\geometry\geometry
.cfg")) (Example.aux)
\openout1 = `Example.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 32.
LaTeX Font Info:    ... okay on input line 32.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 32.
LaTeX Font Info:    ... okay on input line 32.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 32.
LaTeX Font Info:    ... okay on input line 32.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 32.
LaTeX Font Info:    ... okay on input line 32.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 32.
LaTeX Font Info:    ... okay on input line 32.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 32.
LaTeX Font Info:    ... okay on input line 32.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(54.2025pt, 505.89pt, 54.2025pt)
* v-part:(T,H,B)=(43.36243pt, 708.24513pt, 43.36243pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=505.89pt
* \textheight=708.24513pt
* \oddsidemargin=-18.06749pt
* \evensidemargin=-18.06749pt
* \topmargin=-65.90756pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=30.0pt
* \marginparwidth=59.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <10.95> on input line 32.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <8> on input line 32.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <6> on input line 32.

Overfull \hbox (30.0pt too wide) in paragraph at lines 32--32
[] 
 []


Underfull \hbox (badness 10000) in paragraph at lines 41--45

 []


Underfull \hbox (badness 10000) in paragraph at lines 46--48

 []


Overfull \hbox (73.2514pt too wide) in paragraph at lines 88--93
[][] 
 []

[1

{C:/Users/<USER>/AppData/Local/MiKTeX/2.9/pdftex/config/pdftex.map}]
(Example.aux) ) 
Here is how much of TeX's memory you used:
 875 strings out of 493634
 12373 string characters out of 3134830
 73988 words of memory out of 3000000
 4302 multiletter control sequences out of 15000+200000
 7639 words of font info for 27 fonts, out of 3000000 for 9000
 1025 hyphenation exceptions out of 8191
 27i,6n,21p,278b,150s stack positions out of 5000i,500n,10000p,200000b,50000s
<C:/Users/<USER>/AppData/Local/Programs/MiKTeX 2.9/fonts/type1/p
ublic/amsfonts/cm/cmbx10.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX 2.9
/fonts/type1/public/amsfonts/cm/cmbx12.pfb><C:/Users/<USER>/AppData/Local/Progr
ams/MiKTeX 2.9/fonts/type1/public/amsfonts/cm/cmr10.pfb><C:/Users/<USER>/AppDat
a/Local/Programs/MiKTeX 2.9/fonts/type1/public/amsfonts/cm/cmsy10.pfb><C:/Users
/jonfar/AppData/Local/Programs/MiKTeX 2.9/fonts/type1/public/amsfonts/cm/cmti10
.pfb>
Output written on Example.pdf (1 page, 78282 bytes).
PDF statistics:
 26 PDF objects out of 1000 (max. 8388607)
 0 named destinations out of 1000 (max. 500000)
 1 words of extra memory for PDF output out of 10000 (max. 10000000)

