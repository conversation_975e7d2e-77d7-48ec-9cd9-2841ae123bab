This is pdfTeX, Version 3.141592653-2.6-1.40.22 (TeX Live 2022/dev/Debian) (preloaded format=pdflatex 2024.7.20)  13 DEC 2024 16:31
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**/home/<USER>/Documents/projects/CV/energy/FarlandCV.tex
(/home/<USER>/Documents/projects/CV/energy/FarlandCV.tex
LaTeX2e <2021-11-15> patch level 1
L3 programming layer <2022-01-21> (/usr/share/texlive/texmf-dist/tex/latex/base/article.cls
Document Class: article 2021/10/04 v1.4n Standard LaTeX document class
(/usr/share/texlive/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2021/10/04 v1.4n Standard LaTeX file (size option)
)
\c@part=\count185
\c@section=\count186
\c@subsection=\count187
\c@subsubsection=\count188
\c@paragraph=\count189
\c@subparagraph=\count190
\c@figure=\count191
\c@table=\count192
\abovecaptionskip=\skip47
\belowcaptionskip=\skip48
\bibindent=\dimen138
) (/usr/share/texlive/texmf-dist/tex/latex/base/latexsym.sty
Package: latexsym 1998/08/17 v2.2e Standard LaTeX package (lasy symbols)
\symlasy=\mathgroup4
LaTeX Font Info:    Overwriting symbol font `lasy' in version `bold'
(Font)                  U/lasy/m/n --> U/lasy/b/n on input line 52.
) (/usr/share/texlive/texmf-dist/tex/latex/preprint/fullpage.sty
Package: fullpage 1999/02/23 1.1 (PWD)
\FP@margin=\skip49
) (/usr/share/texlive/texmf-dist/tex/latex/titlesec/titlesec.sty
Package: titlesec 2021/07/05 v2.14 Sectioning titles
\ttl@box=\box50
\beforetitleunit=\skip50
\aftertitleunit=\skip51
\ttl@plus=\dimen139
\ttl@minus=\dimen140
\ttl@toksa=\toks16
\titlewidth=\dimen141
\titlewidthlast=\dimen142
\titlewidthfirst=\dimen143
) (/usr/share/texlive/texmf-dist/tex/latex/marvosym/marvosym.sty
Package: marvosym 2011/07/20 v2.2 Martin Vogel's Symbols font definitions
) (/usr/share/texlive/texmf-dist/tex/latex/graphics/color.sty
Package: color 2021/12/07 v1.3c Standard LaTeX Color (DPC)
 (/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: pdftex.def on input line 149.
 (/usr/share/texlive/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2020/10/05 v1.2a Graphics/color driver for pdftex
) (/usr/share/texlive/texmf-dist/tex/latex/graphics/dvipsnam.def
File: dvipsnam.def 2016/06/17 v3.0m Driver-dependent file (DPC,SPQR)
)) (/usr/share/texlive/texmf-dist/tex/latex/tools/verbatim.sty
Package: verbatim 2020-07-07 v1.5u LaTeX2e package for verbatim enhancements
\every@verbatim=\toks17
\verbatim@line=\toks18
\verbatim@in@stream=\read2
) (/usr/share/texlive/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2019/06/20 v3.9 Customized lists
\enitkv@toks@=\toks19
\labelindent=\skip52
\enit@outerparindent=\dimen144
\enit@toks=\toks20
\enit@inbox=\box51
\enit@count@id=\count193
\enitdp@description=\count194
) (/usr/share/texlive/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2021-06-07 v7.00m Hypertext links for LaTeX
 (/usr/share/texlive/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2020-05-10 v1.25 LaTeX kernel commands for general use (HO)
) (/usr/share/texlive/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2020/03/06 v1.0d TeX engine tests
) (/usr/share/texlive/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (/usr/share/texlive/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
) (/usr/share/texlive/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2014/10/28 v1.15 key=value parser (DPC)
\KV@toks@=\toks21
) (/usr/share/texlive/texmf-dist/tex/generic/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2019/12/15 v1.18 Key value parser (HO)
) (/usr/share/texlive/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (/usr/share/texlive/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
) (/usr/share/texlive/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (/usr/share/texlive/texmf-dist/tex/latex/letltxmacro/letltxmacro.sty
Package: letltxmacro 2019/12/03 v1.6 Let assignment for LaTeX macros (HO)
) (/usr/share/texlive/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
) (/usr/share/texlive/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2020-10-07 v3.14 Key value format for package options (HO)
)
\@linkdim=\dimen145
\Hy@linkcounter=\count195
\Hy@pagecounter=\count196
 (/usr/share/texlive/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2021-06-07 v7.00m Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (/usr/share/texlive/texmf-dist/tex/latex/hyperref/hyperref-langpatches.def
File: hyperref-langpatches.def 2021-06-07 v7.00m Hyperref: patches for babel languages
) (/usr/share/texlive/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
) (/usr/share/texlive/texmf-dist/tex/generic/etexcmds/etexcmds.sty
Package: etexcmds 2019/12/15 v1.7 Avoid name clashes with e-TeX commands (HO)
)
\Hy@SavedSpaceFactor=\count197
 (/usr/share/texlive/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2021-06-07 v7.00m Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4192.
Package hyperref Info: Link nesting OFF on input line 4197.
Package hyperref Info: Hyper index ON on input line 4200.
Package hyperref Info: Plain pages OFF on input line 4207.
Package hyperref Info: Backreferencing OFF on input line 4212.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4445.
\c@Hy@tempcnt=\count198
 (/usr/share/texlive/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip16
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4804.
\XeTeXLinkMargin=\dimen146
 (/usr/share/texlive/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (/usr/share/texlive/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count199
\Field@Width=\dimen147
\Fld@charsize=\dimen148
Package hyperref Info: Hyper figures OFF on input line 6076.
Package hyperref Info: Link nesting OFF on input line 6081.
Package hyperref Info: Hyper index ON on input line 6084.
Package hyperref Info: backreferencing OFF on input line 6091.
Package hyperref Info: Link coloring OFF on input line 6096.
Package hyperref Info: Link coloring with OCG OFF on input line 6101.
Package hyperref Info: PDF/A mode OFF on input line 6106.
LaTeX Info: Redefining \ref on input line 6146.
LaTeX Info: Redefining \pageref on input line 6150.
 (/usr/share/texlive/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count266
\c@Item=\count267
\c@Hfootnote=\count268
)
Package hyperref Info: Driver: hpdftex.
 (/usr/share/texlive/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2021-06-07 v7.00m Hyperref driver for pdfTeX
 (/usr/share/texlive/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
)
\Fld@listcount=\count269
\c@bookmark@seq@number=\count270
 (/usr/share/texlive/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2019/12/05 v1.9 Rerun checks for auxiliary files (HO)
 (/usr/share/texlive/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 286.
)
\Hy@SectionHShift=\skip53
) (/usr/share/texlive/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2021/01/28 v4.0.1 Extensive control of page headers and footers
\f@nch@headwidth=\skip54
\f@nch@O@elh=\skip55
\f@nch@O@erh=\skip56
\f@nch@O@olh=\skip57
\f@nch@O@orh=\skip58
\f@nch@O@elf=\skip59
\f@nch@O@erf=\skip60
\f@nch@O@olf=\skip61
\f@nch@O@orf=\skip62
) (/usr/share/texlive/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2022-01-12 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count271
\l__pdf_internal_box=\box52
) (./FarlandCV.aux)
\openout1 = `FarlandCV.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 65.
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 65.
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 65.
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 65.
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 65.
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 65.
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 65.
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 65.
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 65.
LaTeX Font Info:    ... okay on input line 65.
 (/usr/share/texlive/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count272
\scratchdimen=\dimen149
\scratchbox=\box53
\nofMPsegments=\count273
\nofMParguments=\count274
\everyMPshowfont=\toks22
\MPscratchCnt=\count275
\MPscratchDim=\dimen150
\MPnumerator=\count276
\makeMPintoPDFobject=\count277
\everyMPtoPDFconversion=\toks23
)
Package hyperref Info: Link coloring OFF on input line 65.
 (/usr/share/texlive/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2021-04-02 v2.47 Cross-referencing by name of section
 (/usr/share/texlive/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (/usr/share/texlive/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count278
)
LaTeX Info: Redefining \ref on input line 65.
LaTeX Info: Redefining \pageref on input line 65.
LaTeX Info: Redefining \nameref on input line 65.
 (./FarlandCV.out) (./FarlandCV.out)
\@outlinefile=\write3
\openout3 = `FarlandCV.out'.

LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <10.95> on input line 67.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <8> on input line 67.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <6> on input line 67.
LaTeX Font Info:    Trying to load font information for U+lasy on input line 67.
 (/usr/share/texlive/texmf-dist/tex/latex/base/ulasy.fd
File: ulasy.fd 1998/08/17 v2.2e LaTeX symbol font definitions
)

Package fancyhdr Warning: \footskip is too small (0.0pt): 
(fancyhdr)                Make it at least 4.08003pt, for example:
(fancyhdr)                \setlength{\footskip}{4.08003pt}.

[1

{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map}]

Package fancyhdr Warning: \footskip is too small (0.0pt): 
(fancyhdr)                Make it at least 4.08003pt, for example:
(fancyhdr)                \setlength{\footskip}{4.08003pt}.

[2]

Package fancyhdr Warning: \footskip is too small (0.0pt): 
(fancyhdr)                Make it at least 4.08003pt, for example:
(fancyhdr)                \setlength{\footskip}{4.08003pt}.

[3] (./FarlandCV.aux)
Package rerunfilecheck Info: File `FarlandCV.out' has not changed.
(rerunfilecheck)             Checksum: 7AD0505EBB0B9085BB6730F47AC412E9;1158.
 ) 
Here is how much of TeX's memory you used:
 9212 strings out of 478287
 143509 string characters out of 5849289
 456577 words of memory out of 5000000
 27302 multiletter control sequences out of 15000+600000
 475507 words of font info for 50 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 60i,11n,63p,357b,482s stack positions out of 5000i,500n,10000p,200000b,80000s
{/usr/share/texmf/fonts/enc/dvips/cm-super/cm-super-ts1.enc}</usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx12.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmcsc10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmti10.pfb></usr/share/texmf/fonts/type1/public/cm-super/sfrm1000.pfb></usr/share/texmf/fonts/type1/public/cm-super/sfrm1095.pfb>
Output written on FarlandCV.pdf (3 pages, 112809 bytes).
PDF statistics:
 98 PDF objects out of 1000 (max. 8388607)
 78 compressed objects within 1 object stream
 11 named destinations out of 1000 (max. 500000)
 57 words of extra memory for PDF output out of 10000 (max. 10000000)

