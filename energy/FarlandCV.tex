\documentclass[letterpaper,11pt]{article}

\usepackage{latexsym}
\usepackage[empty]{fullpage}
\usepackage{titlesec}
\usepackage{marvosym}
\usepackage[usenames,dvipsnames]{color}
\usepackage{verbatim}
\usepackage{enumitem}
\usepackage[pdftex]{hyperref}
\usepackage{fancyhdr}


\pagestyle{fancy}
\fancyhf{} % clear all header and footer fields
\fancyfoot{}
\renewcommand{\headrulewidth}{0pt}
\renewcommand{\footrulewidth}{0pt}

% Adjust margins
\addtolength{\oddsidemargin}{-0.375in}
\addtolength{\evensidemargin}{-0.375in}
\addtolength{\textwidth}{1in}
\addtolength{\topmargin}{-.5in}
\addtolength{\textheight}{1.0in}

\urlstyle{same}

\raggedbottom
\raggedright
\setlength{\tabcolsep}{0in}

% Sections formatting
\titleformat{\section}{
  \vspace{-4pt}\scshape\raggedright\large
}{}{0em}{}[\color{black}\titlerule \vspace{-5pt}]

%-------------------------
% Custom commands
\newcommand{\resumeItem}[2]{
  \item\small{
    \textbf{#1}{: #2 \vspace{-2pt}}
  }
}

\newcommand{\resumeSubheading}[4]{
  \vspace{10pt}
    \begin{tabular*}{0.97\textwidth}{l@{\extracolsep{\fill}}r}
      \textbf{#1} & #2 \\ 
      \textit{\small#3} & \textit{\small #4} \\
    \end{tabular*}\vspace{-5pt}
}

\newcommand{\resumeSubItem}[2]{\resumeItem{#1}{#2}\vspace{-4pt}}

\renewcommand{\labelitemii}{$\circ$}

\newcommand{\resumeSubHeadingListStart}{\begin{itemize}[leftmargin=*]}
\newcommand{\resumeSubHeadingListEnd}{\end{itemize}}
\newcommand{\resumeItemListStart}{\begin{itemize}}
\newcommand{\resumeItemListEnd}{\end{itemize}\vspace{-5pt}}

%-------------------------------------------
%%%%%%  CV STARTS HERE  %%%%%%%%%%%%%%%%%%%%%%%%%%%%
\begin{document}
%----------HEADING-----------------
\begin{tabular*}{\textwidth}{l@{\extracolsep{\fill}}r}
  \textbf{\Large Jon Farland} & San Francisco Bay Area \\
  \href{mailto:<EMAIL>}{<EMAIL>} & Mobile: ****** 237 8192 \\
  \href{https://www.linkedin.com/in/jonfarland/}{https://www.linkedin.com/in/jonfarland/} & \href{https://github.com/jfarland}{https://github.com/jfarland}
\end{tabular*}

%--------PROGRAMMING SKILLS------------
\section{Technical Strengths}
\vspace{10pt}

\textbf{Deep Learning}{: Causal Language Modeling, Agents, Distillation, Autoencoders, RNN, LSTM, MLP } \\
\textbf{ML - Supervised}{: Timeseries, GAM, Boosting/Bagging, Ensembling, Logistic Regression } \\
\textbf{ML - Unsupervised}{: UMAP, T-SNE, K-means, HDBSCAN, Gaussian Mixture Model} \\
\textbf{Econometrics}{: Fixed/Random Effects, Mixed Effects, Hypothesis Testing, Counterfactual} \\
\textbf{Languages}{: Python, R, SQL, Matlab, Mathematica} \\
\textbf{Computing}{: Unix Systems, Docker, Kubernetes, Spark, AWS, GCP, Azure, Pytorch, H2O.ai} \\

%-----------ACCOMPLISHMENTS-----------------
\section{Accomplishments}
\vspace{10pt}
\textbf{AI/ML} Delivered advanced AI systems across industries including
  \resumeSubHeadingListStart
    \resumeSubItem{\textbf{US Intelligence Community}}{Engagement to develop next-generation Gen AI analytics capabilities in the IC} 
    \resumeSubItem{\textbf{Utilities and Energy Sector}}{Delivered real-time, high-frequency forecasting system using smart meter data still in production today}
    \resumeSubItem{\textbf{Large International Bank}}{Designed and built AI/ML solutions within Fraud Technology and other business units.}
    \resumeSubItem{\textbf{Retail and Supply Chain}}{Built anomaly detection and forecasting systems to reduce inventory costs}
    \resumeSubItem{\textbf{Healthcare}}{Developed dimensionality reduction techniques for use in genomics studies}
    %\resumeSubItem{\textbf{Powur}}{ Multi-year, 6-figure engagement to provide AutoML and ML Ops across solar panel distributor.}
  \resumeSubHeadingListEnd
\vspace{10pt}
\textbf{Talent Development} Grew a division of data scientists, engineers, and subject matter experts from 5 to over 25, mentoring them to drive client-focused solutions and align tightly with overall strategy.

\vspace{10pt}
\textbf{AI Coursework} Developed an AI training course for Fortune 500 executives focused on going end-to-end with Generative AI for the Enterprise. Additional Data Science, ML and Cloud Developer Courses made available on Coursera, Udemy and H2O University

%\textbf{Reduced Sales Cycles} Cut sales cycle times by 25\% by optimizing technical engagement through tailored pre/post sales technical resources, leading to faster deal closures and higher client satisfaction.

%-----------PROFESSIONAL EXPERIENCE-----------------
\section{Professional Experience}
  \resumeSubheading
    {Director of AI Solutions Engineering}{Mountain View, CA}
    {H2O.ai}{Dec 2021 - Present}
    \resumeItemListStart
      \resumeItem{Leadership}
        {Led the customer-facing technology team North America}
      \resumeItem{Data Science}
        {Partnered with customers and top Kaggle Grandmasters to develop advanced Machine Learning applications of Generative AI, Deep Learning, AutoML, Timeseries forecasting and more.}
      \resumeItem{Customer Results}
        {Delivered dozens of successful AI pilots for customers and prospective customers with the majority of which going to production}
      \resumeItem{Partner Engineering}
        {Developed and delivered a scalable enablement plan for technology partners such as Dell, Snowflake and NVIDIA}
      \resumeItemListEnd
  \vspace{5pt}
  \resumeSubheading
    {Senior Data Scientist, Manager}{Oakland, CA}
    {TROVE Predictive Data Science (Acquired by E-Source)}{Oct 2017 - Dec 2021}
    \resumeItemListStart
      \resumeItem{Customer Management}
        {Achieved 100\% renewal rate and managed multi-year engagements to implement AI models across North American utility companies, boosting operational efficiency and enhancing customer outcomes.}
      \resumeItem{AI Model Development}
        {Developed large-scale, near real-time AI systems capable of predicting household-level reductions in energy usage through ensembling, segmentation and data fusion techniques }
  \resumeItemListEnd
  \vspace{5pt}
  \resumeSubheading
    {Senior Technical Consultant, Data Scientist}{Boston, MA}
    {KEMA (Acquired by DNV)}{Aug 2012 - Oct 2017}
    \resumeItemListStart
      \resumeItem{Technical Consulting}
        {Advised on energy modeling and forecasting for clients worldwide, securing multi-million-dollar contracts focused on energy efficiency, demand response, and emerging technologies like electric vehicles, renewables, and non-intrusive load metering.}
    \resumeItemListEnd

%-----------EDUCATION-----------------
\section{Education}
  \resumeSubheading
    {University of Massachusetts}{Amherst, MA}
    {Isenberg School of Management}{Aug 2013}
    \resumeItemListStart
      \resumeItem{Masters of Science}{Applied Econometrics}
      \resumeItem{Bachelors of Business Administration}{Finance and Operations Management, Cum Laude}
    \resumeItemListEnd
  \resumeSubheading
    {University of Naples Federico}{Portici, Italy}
    {Visiting Graduate Study in Advanced Micro-Econometrics}{Aug 2011}

\section{Publications and Presentations}
\vspace{10pt}
	\resumeSubHeadingListStart
    \resumeItem{AI / ML Courses, Coursera}
    {Certified training course for AI engineers and developers (\href{https://www.coursera.org/partners/h2o}{\bf Coursera})}
		\resumeItem{Public GenAI Training Course}
		{End-to-end training course on Generative AI fundamentals (\href{https://github.com/h2oai/h2o_genai_training}{\bf GitHub})}
    \resumeItem{Introduction to Data Science and Machine Learning}
    {A beginner's guide to using AI/ML software (\href{https://www.youtube.com/watch?v=xBfEzm-X-mM}{\bf YouTube})}
		\resumeItem{Snowflake AI Builders Interview Series}
		{Combining Generative and Predictive AI using Agents (\href{https://www.youtube.com/watch?v=NlwiA0BAVBw}{\bf YouTube})}
    \resumeItem{\bf Commandcast: AI Agents for Timeseries Forecasting}
    {Open source project focused on being able to use AI Agentic workflows for timeseries forecasting (\href{https://github.com/jfarland/commandcast}{\bf GitHub})}
		\resumeItem{\bf TimeGPT: Foundational Timeseries Model}
    {Launch of the world's first transformer-based model for time series forecasting (\href{https://www.youtube.com/watch?v=N0gyDVUFPlg}{\bf YouTube})}
		\resumeItem{Value in Energy Data}
    {How Generative AI is being used across the energy industry (\href{https://www.youtube.com/watch?v=jj_9W7glP48}{\bf YouTube})}
		\resumeItem{Research Thesis}{Zonal and Regional Load Foreccasting the New England Wholesale Electricity Market: a Semiparametric Regression Approach, University of Massachusetts, Amherst, 2013}
    \resumeItem{Rolling up IOU Account-Level Data to Measure Savings from the Top-Down}{N. Stevens, M. Cohen, A. Stryker, J. Farland, P. Rathbun}
    \resumeItem{Utility Load Research: The Future of Load Research is Now}{C. Puckett, C. Williamson, C. Godin, W. Gifford, J. Farland, T. Laing, T. Hong, IEEE Power and Energy Magazine, May/June, 2020}
		\resumeItem{Model-based Matching and Other Benefits of High Frequency Interval Data}{L. Getachew, J. Farland, K. Agnew, P. Franzese, V. Richardson, G. Sadhasivan, International Energy Program Evaluation Conference, Baltimore, USA, 2017}
		\resumeItem{Electricity End Use Forecasting Using Non-Intrusive Load Metering Technology}{J. Farland, C. Puckett, F. Coito, International Symposium on Forecasting, Cairns, Australia, 2017}
		\resumeItem{High Resolution Energy Modeling that Scales with Apache Spark 2.0}{J. Farland, Spark Summit Boston, USA, 2017}
		\resumeItem{Load Forecasting with Distributed Energy Resources}{J. Farland, F. Farzan, R.J. Hyndman, International Symposium on Forecasting, Santander, Spain, 2016}
		\resumeItem{Breaking Down Analytical and Computational Barriers in Energy Data Analytics}{J. Farland, Spark Summit San Francisco, USA, 2016}
	\resumeSubHeadingListEnd

%-----------PROJECTS-----------------
\section{Selected Project Experience}
  \resumeSubHeadingListStart
    \resumeSubItem{AI Agentic Workflows for Market Research, San Francisco, CA}
    {Chief architect of multi-agentic solution to simulate character personas for marketing research, focus groups and other applications}
  	\resumeSubItem{Generative AI for Regulatory Compliance, McLean, VA}
    {Led a Generative AI project to detect banking violations, enhancing regulatory compliance efficiency by automating the detection and reporting process.}
    \resumeSubItem{AI for Genomics Pilot, Boston, MA}
    {Developed a high-performance classifier that predicted cancer diagnoses, increasing accuracy by leveraging advanced data science techniques.}
    \resumeSubItem{Enterprise GenAI Deployment, San Francisco, CA}
    {Architected a secure deployment framework for Generative AI in highly regulated environments, including air-gapped scenarios for government clients.}
      \resumeSubItem{Predictive Analytics for Utility DR Operations, Project Manager, San Francisco, California}
      {Designed and built a large-scale Demand Response forecasting system for Pacific Gas and Electric, covering over 7 million meters and various demand response programs.}
    
    \resumeSubItem{Advanced Electric Distribution Grid Forecasting, Data Scientist, San Francisco, California}
      {Developed a forecasting system for energy demand across two large utility operating areas in California, enabling significant cost savings by reducing the need for expensive sensors.}
    
    \resumeSubItem{Transformer Asset Risk Modeling, Data Scientist, Kansas City, Kansas}
      {Used Dissolved Gas Analysis (DGA) testing data to predict failure modes and time-to-failure for electrical transformers. Classified transformers based on risk levels.}
    
    \resumeSubItem{Real-time Customer Baseline and Financial Settlement Modeling, Data Scientist, Portland, Oregon}
      {Generated counterfactual customer-level baselines using ensemble approaches such as similar-day, regression, and gradient boosting machine models for Peak Time Rebates program.}
    
    \resumeSubItem{Hierarchical Forecasting of Energy and Peak Demand in Saudi Arabia, Senior Data Scientist, Riyadh, Saudi Arabia}
      {Developed predictive analytics for the largest end-use metering project in the world, training Saudi Aramco, Saudi Electricity Company, and regulatory staff on predictive analytics.}
  
  \resumeSubHeadingListEnd

\section{Professional Organizations and Distinctions}
\textbf{Peak Load Management Alliance (PLMA)}{ Board of Directors, 2020-2021} \\
\textbf{IEEE}{ Member} \\
\textbf{International Institute of Forecasting}{ Member} \\
\textbf{International Symposium on Forecasting}{ Session Chair / Invited Speaker, 2014-2023} \\
\textbf{Spark Summit}{ Speaker, 2016, 2017}\\
\textbf{Spark Advisory Forum}{ Speaker, 2016}\\
\textbf{University of Massachusetts Amherst} { Vijay Bhagavan Distinguished Teaching Award, 2012}\\
\textbf{Italian Ministry of Agriculture} { International Advanced Econometrics Scholarship, 2011}\\

%-------------------------------------------
\end{document}
