\documentclass[letterpaper,11pt]{article}

\usepackage{latexsym}
\usepackage[empty]{fullpage}
\usepackage{titlesec}
\usepackage{marvosym}
\usepackage[usenames,dvipsnames]{color}
\usepackage{verbatim}
\usepackage{enumitem}
\usepackage[pdftex]{hyperref}
\usepackage{fancyhdr}


\pagestyle{fancy}
\fancyhf{} % clear all header and footer fields
\fancyfoot{}
\renewcommand{\headrulewidth}{0pt}
\renewcommand{\footrulewidth}{0pt}

% Adjust margins
\addtolength{\oddsidemargin}{-0.375in}
\addtolength{\evensidemargin}{-0.375in}
\addtolength{\textwidth}{1in}
\addtolength{\topmargin}{-.5in}
\addtolength{\textheight}{1.0in}

\urlstyle{same}

\raggedbottom
\raggedright
\setlength{\tabcolsep}{0in}

% Sections formatting
\titleformat{\section}{
  \vspace{-4pt}\scshape\raggedright\large
}{}{0em}{}[\color{black}\titlerule \vspace{-5pt}]

%-------------------------
% Custom commands
\newcommand{\resumeItem}[2]{
  \item\small{
    \textbf{#1}{: #2 \vspace{-2pt}}
  }
}

\newcommand{\resumeSubheading}[4]{
  \vspace{10pt}
    \begin{tabular*}{0.97\textwidth}{l@{\extracolsep{\fill}}r}
      \textbf{#1} & #2 \\ 
      \textit{\small#3} & \textit{\small #4} \\
    \end{tabular*}\vspace{-5pt}
}

\newcommand{\resumeSubItem}[2]{\resumeItem{#1}{#2}\vspace{-4pt}}

\renewcommand{\labelitemii}{$\circ$}

\newcommand{\resumeSubHeadingListStart}{\begin{itemize}[leftmargin=*]}
\newcommand{\resumeSubHeadingListEnd}{\end{itemize}}
\newcommand{\resumeItemListStart}{\begin{itemize}}
\newcommand{\resumeItemListEnd}{\end{itemize}\vspace{-5pt}}

%-------------------------------------------
%%%%%%  CV STARTS HERE  %%%%%%%%%%%%%%%%%%%%%%%%%%%%
\begin{document}
%----------HEADING-----------------
\begin{tabular*}{\textwidth}{l@{\extracolsep{\fill}}r}
  \textbf{\Large Jon Farland} & San Francisco Bay Area \\
  \href{mailto:<EMAIL>}{<EMAIL>} & Mobile: ****** 237 8192 \\
  \href{https://www.linkedin.com/in/jonfarland/}{https://www.linkedin.com/in/jonfarland/} & \href{https://github.com/jfarland}{https://github.com/jfarland}
\end{tabular*}

%--------PROGRAMMING SKILLS------------
\section{Technical Experience and Research Areas}
\vspace{5pt}

\textbf{Deep Learning}{: Causal Language Modeling, Agents, Distillation, Autoencoders, RNN, LSTM, MLP } \\
\textbf{ML - Supervised}{: Timeseries, GAM, Boosting/Bagging, Ensembling, Classification} \\
\textbf{ML - Unsupervised}{: UMAP, T-SNE, K-means, HDBSCAN, Gaussian Mixture Model} \\
\textbf{Econometrics}{: Fixed/Random Effects, Mixed Effects, Hypothesis Testing, Experimental Design} \\
\textbf{Languages}{: Python, R, SQL, Matlab, Mathematica} \\
\textbf{Computing}{: Unix Systems, Docker, Kubernetes, Spark, AWS, GCP, Azure, Pytorch, H2O.ai} \\

%-----------ACCOMPLISHMENTS-----------------
\section{Accomplishments}
\vspace{5pt}
\textbf{AI/ML} Lead the design and development of advanced AI systems still in production across several industries. These include:
\vspace{5pt}
  \resumeSubHeadingListStart
    \resumeSubItem{\textbf{US Intelligence Community}}{Engagement to develop next-generation Gen AI analytics capabilities in the IC} 
    \resumeSubItem{\textbf{Utilities and Energy Sector}}{Delivered real-time, high-frequency forecasting system using smart meter data still in production today}
    \resumeSubItem{\textbf{Large International Bank}}{Designed and built AI/ML solutions within Fraud Technology and other business units.}
    \resumeSubItem{\textbf{Retail and Supply Chain}}{Built anomaly detection and forecasting systems to reduce inventory costs}
    \resumeSubItem{\textbf{Healthcare}}{Developed dimensionality reduction techniques for use in genomics studies}
    %\resumeSubItem{\textbf{Powur}}{ Multi-year, 6-figure engagement to provide AutoML and ML Ops across solar panel distributor.}
  \resumeSubHeadingListEnd
\vspace{5pt}
\textbf{Talent Development} Grew a division of data scientists, engineers, and subject matter experts from 5 to over 25, mentoring them to drive client-focused solutions and align tightly with overall strategy.

\vspace{5pt}
\textbf{AI Coursework} Developed several AI training courses for Fortune 500 executives as well as practitioners focused on going end-to-end with Generative AI for the Enterprise. Additional Data Science, ML and Cloud Developer Courses made available on Coursera, Udemy and H2O University

%\textbf{Reduced Sales Cycles} Cut sales cycle times by 25\% by optimizing technical engagement through tailored pre/post sales technical resources, leading to faster deal closures and higher client satisfaction.

%-----------PROFESSIONAL EXPERIENCE-----------------
\section{Professional Experience}
  \resumeSubheading
    {Director of AI Solutions Engineering}{Mountain View, CA}
    {H2O.ai}{Dec 2021 - Present}
    \resumeItemListStart
      \resumeItem{Leadership}
        {Led the customer-facing technology team North America}
      \resumeItem{Data Science}
        {Partnered with customers and top Kaggle Grandmasters to develop advanced Machine Learning applications of Generative AI, Deep Learning, AutoML, Timeseries forecasting and more.}
      \resumeItem{Customer Results}
        {Delivered dozens of successful AI pilots for customers and prospective customers with the majority of which going to production}
      \resumeItem{Partner Engineering}
        {Developed and delivered a scalable enablement plan for technology partners such as Dell, Snowflake and NVIDIA}
      \resumeItemListEnd
  \vspace{5pt}
  \resumeSubheading
    {Senior Data Scientist, Manager}{Oakland, CA}
    {TROVE Predictive Data Science (Acquired by E-Source)}{Oct 2017 - Dec 2021}
    \resumeItemListStart
      \resumeItem{Customer Management}
        {Achieved 100\% renewal rate and managed multi-year engagements to implement AI models across North American utility companies, boosting operational efficiency and enhancing customer outcomes.}
      \resumeItem{AI Model Development}
        {Developed large-scale, near real-time AI systems capable of predicting household-level reductions in energy usage through ensembling, segmentation and data fusion techniques }
  \resumeItemListEnd
  \vspace{5pt}
  \resumeSubheading
    {Senior Technical Consultant, Data Scientist}{Boston, MA}
    {KEMA (Acquired by DNV)}{Aug 2012 - Oct 2017}
    \resumeItemListStart
      \resumeItem{Technical Consulting}
        {Advised on energy modeling and forecasting for clients worldwide, securing multi-million-dollar contracts focused on energy efficiency, demand response, and emerging technologies like electric vehicles, renewables, and non-intrusive load metering.}
    \resumeItemListEnd

%-----------EDUCATION-----------------
\section{Education}
  \resumeSubheading
    {University of Massachusetts}{Amherst, MA}
    {Isenberg School of Management}{Aug 2013}
    \resumeItemListStart
      \resumeItem{Masters of Science}{Applied Econometrics}
      \resumeItem{Bachelors of Business Administration}{Finance and Operations Management, Cum Laude}
    \resumeItemListEnd
  \resumeSubheading
    {University of Naples Federico}{Portici, Italy}
    {Visiting Graduate Study in Advanced Micro-Econometrics}{Aug 2011}

%-----------PROJECTS-----------------
\vspace{5pt}
\section{Recent Presentations}
\vspace{5pt}
  \resumeSubHeadingListStart

    \resumeSubItem{AI / ML Courses, Coursera}
    {Certified training course for AI engineers and developers (\href{https://www.coursera.org/partners/h2o}{\bf Coursera})}
    \resumeSubItem{Public Generative AI Training Course}
    {End-to-end training course on Generative AI fundamentals (\href{https://github.com/h2oai/h2o_genai_training}{\bf GitHub})}
    \resumeSubItem{\bf Commandcast: AI Agents for Timeseries Forecasting}
    {Open source project focused on being able to use AI Agentic workflows for timeseries forecasting (\href{https://github.com/jfarland/commandcast}{\bf GitHub})}
    \resumeSubItem{\bf TimeGPT: Foundational Timeseries Model}
    {Launch of the world's first transformer-based model for time series forecasting (\href{https://www.youtube.com/watch?v=N0gyDVUFPlg}{\bf YouTube})}
    \resumeSubItem{Introduction to Data Science and Machine Learning}
    {A beginner's guide to using AI/ML software (\href{https://www.youtube.com/watch?v=xBfEzm-X-mM}{\bf YouTube})}
    \resumeSubItem{Snowflake AI Builders Interview Series}
    {Combining Generative and Predictive AI using Agents (\href{https://www.youtube.com/watch?v=NlwiA0BAVBw}{\bf YouTube})}
    \resumeSubItem{Value in Energy Data}
    {How Generative AI is Being Used Across The Energy Industry (\href{https://www.youtube.com/watch?v=jj_9W7glP48}{\bf YouTube})}
  \resumeSubHeadingListEnd

\vspace{5pt}
\section{Select Project Work}
\vspace{5pt}
  \resumeSubHeadingListStart

    \resumeSubItem{AI Agentic Personas for Product Marketing, Chief Architect, San Francisco, CA}
    {Developed a multi-agentic solution allowing customers to design their own AI personas. Each persona is defined by system prompt descriptions as well as quantitative and qualitative data. These personas are also given agency through tools for web search, coding, RAG and more.}
  	
    \resumeSubItem{Generative AI for Regulatory Compliance, Technical Advisor, McLean, VA}
    {Led a Generative AI project to detect banking violations, enhancing regulatory compliance efficiency by automating the detection and reporting process. This pilot successfully showed how a system can be engineered to retrieve daily news articles and public filing records, ingested into vector or graph schema, and predictive models developed to identify early stage compliance violations}
  
    \resumeSubItem{Writing a Winning Proposal using Agentic Reasoning, Developer, San Francisco, CA}
    {Developed a Python application capable of processing RFP/RFI documents, extracting key requirements, matching pre-defined core competancies and finally writing a proposal response. As a result, organizations adopting this still require human-in-the-loop activities while responding to RFPs, but are now enabled to quickly iterate initial drafts and thus respond to more RFPs in the same amount of time.}

    \resumeSubItem{Early Cancer Detection, Senior Data Scientist, Boston, MA}
    {Developed a classifier that predicted cancer diagnoses, increasing accuracy by leveraging autoencoders, boosting algorithms and ensembling.}

    \resumeSubItem{Hierarchical Forecasting of Energy and Peak Demand in Saudi Arabia, Senior Data Scientist, Riyadh, Saudi Arabia}
    {Developed predictive analytics for the largest end-use metering project in the world, training Saudi Aramco, Saudi Electricity Company, and regulatory staff on predictive analytics.}

    \resumeSubItem{Advanced Electric Distribution Grid Forecasting, Data Scientist, San Francisco, California}
    {Developed a forecasting system for energy demand across California by fusing data from disparate telemetry and sensor systems. The improved visibility into forecasted load across the grid enabling significant cost savings by reducing the need for expensive sensors.}

    \resumeSubItem{Predictive Analytics for Utility DR Operations, Project Manager, San Francisco, California}
    {Designed and built a large-scale Demand Response forecasting system for Pacific Gas and Electric, covering over 7 million meters and various demand response programs.}
    
    \resumeSubItem{Transformer Asset Risk Modeling, Data Scientist, Kansas City, Kansas}
    {Used Dissolved Gas Analysis (DGA) testing data to predict failure modes and time-to-failure for electrical transformers. Classified transformers based on risk levels.}
    
    \resumeSubItem{Real-time Customer Baseline and Financial Settlement Modeling, Data Scientist, Portland, Oregon}
    {Generated counterfactual customer-level baselines using ensemble approaches such as similar-day, regression, and gradient boosting machine models for Peak Time Rebates program.}
    

  \resumeSubHeadingListEnd


\section{Publications}
\vspace{5pt}
  \resumeSubHeadingListStart
    \resumeItem{Research Thesis}{Zonal and Regional Load Foreccasting the New England Wholesale Electricity Market: a Semiparametric Regression Approach, University of Massachusetts, Amherst, 2013 \href{https://forecasters.org/wp-content/uploads/gravity_forms/7-2a51b93047891f1ec3608bdbd77ca58d/2014/07/Farland_Jonathan_ISF2014.pdf}{\bf  Scholar Works Link}}
    \resumeItem{Rolling up IOU Account-Level Data to Measure Savings from the Top-Down}{N. Stevens, M. Cohen, A. Stryker, J. Farland, P. Rathbun}
    \resumeItem{Utility Load Research: The Future of Load Research is Now}{C. Puckett, C. Williamson, C. Godin, W. Gifford, J. Farland, T. Laing, T. Hong, IEEE Power and Energy Magazine, May/June, 2020}
    \resumeItem{Model-based Matching and Other Benefits of High Frequency Interval Data}{L. Getachew, J. Farland, K. Agnew, P. Franzese, V. Richardson, G. Sadhasivan, International Energy Program Evaluation Conference, Baltimore, USA, 2017}
    \resumeItem{Electricity End Use Forecasting Using Non-Intrusive Load Metering Technology}{J. Farland, C. Puckett, F. Coito, International Symposium on Forecasting, Cairns, Australia, 2017}
    \resumeItem{High Resolution Energy Modeling that Scales with Apache Spark 2.0}{J. Farland, Spark Summit Boston, USA, 2017}
    \resumeItem{Load Forecasting with Distributed Energy Resources}{J. Farland, F. Farzan, R.J. Hyndman, International Symposium on Forecasting, Santander, Spain, 2016}
    \resumeItem{Breaking Down Analytical and Computational Barriers in Energy Data Analytics}{J. Farland, Spark Summit San Francisco, USA, 2016}
  \resumeSubHeadingListEnd


\section{Professional Organizations and Distinctions}

\resumeSubheading
{Board of Directors}{USA}
{Peak Load Management Aliance (PLMA)}{2020-2021}

\resumeSubheading
{Session Chair, Invited Speaker}{Global}
{International Institute of Forecasting}{2014-2024}

\resumeSubheading
{Invited Speaker}{Global}
{Spark Summit and Advisory Forum}{2016-2017}

\resumeSubheading
{Vijay Bhagavan Distinguished Teaching Award}{Massachusetts, USA}
{University of Massachusetts, Amherst}{2012}

\resumeSubheading
{International Advanced Econometrics Scholarship}{Naples, Italy}
{Italian Ministry of Agriculture}{2011}

\resumeSubheading
{Member}{Global}
{IEEE}{2016-present}



%-------------------------------------------
\end{document}
