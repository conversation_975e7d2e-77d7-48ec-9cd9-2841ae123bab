%-------------------------
% Resume in Latex
% Author : So<PERSON><PERSON><PERSON>j
% Website: https://github.com/sb2nov/resume
% License : MIT
%------------------------

\documentclass[letterpaper,11pt]{article}

\usepackage{latexsym}
\usepackage[empty]{fullpage}
\usepackage{titlesec}
\usepackage{marvosym}
\usepackage[usenames,dvipsnames]{color}
\usepackage{verbatim}
\usepackage{enumitem}
\usepackage[pdftex]{hyperref}
\usepackage{fancyhdr}


\pagestyle{fancy}
\fancyhf{} % clear all header and footer fields
\fancyfoot{}
\renewcommand{\headrulewidth}{0pt}
\renewcommand{\footrulewidth}{0pt}

% Adjust margins
\addtolength{\oddsidemargin}{-0.375in}
\addtolength{\evensidemargin}{-0.375in}
\addtolength{\textwidth}{1in}
\addtolength{\topmargin}{-.5in}
\addtolength{\textheight}{1.0in}

\urlstyle{same}

\raggedbottom
\raggedright
\setlength{\tabcolsep}{0in}

% Sections formatting
\titleformat{\section}{
  \vspace{-4pt}\scshape\raggedright\large
}{}{0em}{}[\color{black}\titlerule \vspace{-5pt}]

%-------------------------
% Custom commands
\newcommand{\resumeItem}[2]{
  \item\small{
    \textbf{#1}{: #2 \vspace{-2pt}}
  }
}

\newcommand{\resumeSubheading}[4]{
  \vspace{10pt}
    \begin{tabular*}{0.97\textwidth}{l@{\extracolsep{\fill}}r}
      \textbf{#1} & #2 \\ 
      \textit{\small#3} & \textit{\small #4} \\
    \end{tabular*}\vspace{-5pt}
}

\newcommand{\resumeSubItem}[2]{\resumeItem{#1}{#2}\vspace{-4pt}}

\renewcommand{\labelitemii}{$\circ$}

\newcommand{\resumeSubHeadingListStart}{\begin{itemize}[leftmargin=*]}
\newcommand{\resumeSubHeadingListEnd}{\end{itemize}}
\newcommand{\resumeItemListStart}{\begin{itemize}}
\newcommand{\resumeItemListEnd}{\end{itemize}\vspace{-5pt}}

%-------------------------------------------
%%%%%%  CV STARTS HERE  %%%%%%%%%%%%%%%%%%%%%%%%%%%%
\begin{document}
%----------HEADING-----------------
\begin{tabular*}{\textwidth}{l@{\extracolsep{\fill}}r}
  \textbf{\Large Jon Farland} & San Francisco Bay Area \\
  \href{mailto:<EMAIL>}{<EMAIL>} & Mobile: ****** 237 8192 \\
  \href{https://www.linkedin.com/in/jonfarland/}{https://www.linkedin.com/in/jonfarland/} & \href{https://github.com/jfarland}{https://github.com/jfarland}
\end{tabular*}


%--------PROGRAMMING SKILLS------------
\section{Technical Experience and Research Areas}
\vspace{5pt}

\textbf{Deep Learning}{: Autoregressive Language Modeling, Agents, Distillation, Autoencoders, RNN, LSTM, MLP } \\
\textbf{ML - Supervised}{: Timeseries, GAM, Boosting/Bagging, Ensembling, Classification} \\
\textbf{ML - Unsupervised}{: UMAP, T-SNE, K-means, DBSCAN, Gaussian Mixture Model} \\
\textbf{Econometrics}{: Fixed/Random Effects, Mixed Effects, Hypothesis Testing, Experimental Design} \\
\textbf{Languages}{: Python, R, SQL, Matlab, Mathematica} \\
\textbf{Computing}{: Unix System, Docker, Kubernetes, Spark, AWS, GCP, Azure, Pytorch, H2O.ai} \\


%-----------ACCOMPLISHMENTS-----------------
\section{Accomplishments}
\vspace{5pt}
\textbf{AI/ML} Lead the design and development of advanced AI systems in production across customers in Financial Services and Banking. These include but are not limited to:
\vspace{5pt}
  \resumeSubHeadingListStart
    \resumeSubItem{\textbf{Fraud Detection}}{Identified millions of dollars in fraudulent activity by developing solutions for check fraud, transaction fraud, know-your-customer, mule account identification and more.} 
    \resumeSubItem{\textbf{Regulatory Compliance}}{Reduced compliance requirements by leveraging novel deep research techniques within an agentic AI framework for compliance with regulatory authorities.}
    \resumeSubItem{\textbf{Forecasting and Anomaly Detection}}{Reduced millions of dollars in operational waste by developing on-demand forecasting systems for applications for call center forecasting, socioeconomic forecasting and timeseries.}
    \resumeSubItem{\textbf{Churn Prediction}}{Developed multi-stage system to reduce customer churn through generative and predictive models.}
  \resumeSubHeadingListEnd

\vspace{5pt}
\textbf{AI Engineering} Several notable AI Engineering systems in production in the US Intelligence Community and industries such as Utilities and Renewable Energy, Investment Banking, Telco and Healthcare.

% \vspace{5pt}
% \textbf{Generative AI Training Program} Developed several AI training courses for Fortune 500 executives as well as practitioners focused on going end-to-end with Generative AI for the Enterprise. Additional Data Science, ML and Cloud Developer Courses made available on Coursera, Udemy and H2O University

%-----------PROFESSIONAL EXPERIENCE-----------------
\section{Professional Experience}
  \resumeSubheading
    {Director of AI Solutions Engineering}{Mountain View, CA}
    {H2O.ai}{Dec 2021 - Present}
    \resumeItemListStart
      \resumeItem{Leadership}
        {Led the technical customer-facing team across both private and public sector customers in North America. Grew a division of data scientists, engineers, and subject matter experts from 5 to over 25, mentoring them to drive client-focused solutions and align tightly with overall strategy.}
      \resumeItem{Strategy}
        {Established strategic partnerships with other technology vendors and business partners such as AWS, Snowflake and Dell to achieve a scalable commercial model for AI software}
        %{Implemented process changes that increased technical win rates by 90\%, including Success Criteria Catalogs and deeper technical engagement throughout the sales process.}
      \resumeItem{AI Engineering}
        {Partnered with customers and Kaggle Grandmasters to develop advanced applications of Generative AI, Deep Learning, AutoML, Timeseries forecasting and more.}
    \resumeItemListEnd

  \resumeSubheading
    {Senior Data Science Manager}{Oakland, CA}
    {TROVE Predictive Data Science (Acquired by E-Source)}{Oct 2017 - Dec 2021}
    \resumeItemListStart
      \resumeItem{Leadership}
        {Lead the data science team focused on modeling consumer behaviour. Products were developed to implement AI models across North American utility companies, boosting operational efficiency and enhancing customer outcomes.}
      \resumeItem{AI Model Development}
        {Developed large-scale, near real-time AI systems capable of predicting household-level reductions in energy usage through ensembling, segmentation and data fusion techniques }
    \resumeItemListEnd

  \resumeSubheading
    {Senior Technical Consultant, Data Scientist}{Boston, US}
    {KEMA (Acquired by DNV)}{Aug 2012 - Oct 2017}
    \resumeItemListStart
      \resumeItem{Impact Evaluation}
        {Built large fixed-effects models to quantitatively measure the impact of programs aimed at increasing energy efficiency and demand response. }
      \resumeItem{Global Energy Forecasting}
        {Advised on energy modeling and forecasting for global clients, securing multi-million-dollar contracts focused on energy efficiency, demand response, and emerging technologies like electric vehicles, renewables, and non-intrusive load metering.}
    \resumeItemListEnd

%-----------EDUCATION-----------------
\section{Education}
  \resumeSubheading
    {University of Massachusetts}{Amherst, MA}
    {Isenberg School of Management}{Aug 2013}
    \resumeItemListStart
      \resumeItem{Masters of Science}{Applied Econometrics and Forecasting}
      \resumeItem{Bachelors of Business Administration}{Finance and Operations Management, Cum Laude}
    \resumeItemListEnd
  \resumeSubheading
    {University of Naples Federico}{Portici, Italy}
    {Visiting Graduate Study in Advanced Micro-Econometrics}{Aug 2011}

%-----------PROJECTS-----------------
\vspace{5pt}
\section{Recent Presentations}
\vspace{5pt}
  \resumeSubHeadingListStart

    \resumeSubItem{AI / ML Courses, Coursera}
    {Certified training course for AI engineers and developers (\href{https://www.coursera.org/partners/h2o}{\bf Coursera})}
    \resumeSubItem{Public Generative AI Training Course}
    {End-to-end training course on Generative AI fundamentals (\href{https://github.com/h2oai/h2o_genai_training}{\bf GitHub})}
    \resumeSubItem{\bf Commandcast: AI Agents for Timeseries Forecasting}
    {Open source project focused on being able to use AI Agentic workflows for timeseries forecasting (\href{https://github.com/jfarland/commandcast}{\bf GitHub})}
    \resumeSubItem{\bf TimeGPT: Foundational Timeseries Model}
    {Launch of the world's first transformer-based model for time series forecasting (\href{https://www.youtube.com/watch?v=N0gyDVUFPlg}{\bf YouTube})}
    \resumeSubItem{Introduction to Data Science and Machine Learning}
    {A beginner's guide to using AI/ML software (\href{https://www.youtube.com/watch?v=xBfEzm-X-mM}{\bf YouTube})}
    \resumeSubItem{Snowflake AI Builders Interview Series}
    {Combining Generative and Predictive AI using Agents (\href{https://www.youtube.com/watch?v=NlwiA0BAVBw}{\bf YouTube})}
    \resumeSubItem{Value in Energy Data}
    {How Generative AI is Being Used Across The Energy Industry (\href{https://www.youtube.com/watch?v=jj_9W7glP48}{\bf YouTube})}
  \resumeSubHeadingListEnd


%-----------PROJECTS-----------------
\vspace{5pt}
\section{Select Project Work}
\vspace{5pt}
  \resumeSubHeadingListStart

    \resumeSubItem{AI Agentic Personas for Product Marketing, Chief Architect, San Francisco, CA}
    {Developed a multi-agentic solution allowing customers to design their own AI personas. Each persona is defined by system prompt descriptions as well as quantitative and qualitative data. These personas are also given agency through tools for web search, coding, RAG and more.}
  	
    \resumeSubItem{Generative AI for Regulatory Compliance, Technical Advisor, McLean, VA}
    {Led a Generative AI project to detect banking violations, enhancing regulatory compliance efficiency by automating the detection and reporting process. This pilot successfully showed how a system can be engineered to retrieve daily news articles and public filing records, ingested into vector or graph schema, and predictive models developed to identify early stage compliance violations}
  
    \resumeSubItem{Writing a Winning Proposal using Agentic Reasoning, Developer, San Francisco, CA}
    {Developed a Python application capable of processing RFP/RFI documents, extracting key requirements, matching pre-defined core competancies and finally writing a proposal response. As a result, organizations adopting this still require human-in-the-loop activities while responding to RFPs, but are now enabled to quickly iterate initial drafts and thus respond to more RFPs in the same amount of time.}

    \resumeSubItem{Early Cancer Detection, Senior Data Scientist, Boston, MA}
    {Developed a classifier that predicted cancer diagnoses, increasing accuracy by leveraging autoencoders, boosting algorithms and ensembling.}

    \resumeSubItem{Hierarchical Forecasting of Energy and Peak Demand in Saudi Arabia, Senior Data Scientist, Riyadh, Saudi Arabia}
    {Developed predictive analytics for the largest end-use metering project in the world, training Saudi Aramco, Saudi Electricity Company, and regulatory staff on predictive analytics.}

  \resumeSubHeadingListEnd


  \section{Publications}
  \vspace{5pt}
    \resumeSubHeadingListStart
      \resumeItem{Research Thesis}{Zonal and Regional Load Foreccasting the New England Wholesale Electricity Market: a Semiparametric Regression Approach, University of Massachusetts, Amherst, 2013 \href{https://forecasters.org/wp-content/uploads/gravity_forms/7-2a51b93047891f1ec3608bdbd77ca58d/2014/07/Farland_Jonathan_ISF2014.pdf}{\bf  Scholar Works Link}}
      \resumeItem{Rolling up IOU Account-Level Data to Measure Savings from the Top-Down}{N. Stevens, M. Cohen, A. Stryker, J. Farland, P. Rathbun}
      \resumeItem{Utility Load Research: The Future of Load Research is Now}{C. Puckett, C. Williamson, C. Godin, W. Gifford, J. Farland, T. Laing, T. Hong, IEEE Power and Energy Magazine, May/June, 2020}
      \resumeItem{Model-based Matching and Other Benefits of High Frequency Interval Data}{L. Getachew, J. Farland, K. Agnew, P. Franzese, V. Richardson, G. Sadhasivan, International Energy Program Evaluation Conference, Baltimore, USA, 2017}
      \resumeItem{Electricity End Use Forecasting Using Non-Intrusive Load Metering Technology}{J. Farland, C. Puckett, F. Coito, International Symposium on Forecasting, Cairns, Australia, 2017}
      \resumeItem{High Resolution Energy Modeling that Scales with Apache Spark 2.0}{J. Farland, Spark Summit Boston, USA, 2017}
      \resumeItem{Load Forecasting with Distributed Energy Resources}{J. Farland, F. Farzan, R.J. Hyndman, International Symposium on Forecasting, Santander, Spain, 2016}
      \resumeItem{Breaking Down Analytical and Computational Barriers in Energy Data Analytics}{J. Farland, Spark Summit San Francisco, USA, 2016}
    \resumeSubHeadingListEnd
  

  \section{Professional Organizations and Distinctions}

  \resumeSubheading
  {Board of Directors}{USA}
  {Peak Load Management Aliance (PLMA)}{2020-2021}
  
  \resumeSubheading
  {Session Chair, Invited Speaker}{Global}
  {International Institute of Forecasting}{2014-2024}
  
  \resumeSubheading
  {Invited Speaker}{Global}
  {Spark Summit and Advisory Forum}{2016-2017}
  
  \resumeSubheading
  {Vijay Bhagavan Distinguished Teaching Award}{Massachusetts, USA}
  {University of Massachusetts, Amherst}{2012}
  
  \resumeSubheading
  {International Advanced Econometrics Scholarship}{Naples, Italy}
  {Italian Ministry of Agriculture}{2011}
  
  \resumeSubheading
  {Member}{Global}
  {IEEE}{2016-present}
  

%-------------------------------------------
\end{document}
