%-------------------------
% Resume in Latex
% Author : So<PERSON><PERSON><PERSON>j
% Website: https://github.com/sb2nov/resume
% License : MIT
%------------------------

\documentclass[letterpaper,11pt]{article}

\usepackage{latexsym}
\usepackage[empty]{fullpage}
\usepackage{titlesec}
\usepackage{marvosym}
\usepackage[usenames,dvipsnames]{color}
\usepackage{verbatim}
\usepackage{enumitem}
\usepackage[pdftex]{hyperref}
\usepackage{fancyhdr}


\pagestyle{fancy}
\fancyhf{} % clear all header and footer fields
\fancyfoot{}
\renewcommand{\headrulewidth}{0pt}
\renewcommand{\footrulewidth}{0pt}

% Adjust margins
\addtolength{\oddsidemargin}{-0.375in}
\addtolength{\evensidemargin}{-0.375in}
\addtolength{\textwidth}{1in}
\addtolength{\topmargin}{-.5in}
\addtolength{\textheight}{1.0in}

\urlstyle{same}

\raggedbottom
\raggedright
\setlength{\tabcolsep}{0in}

% Sections formatting
\titleformat{\section}{
  \vspace{-4pt}\scshape\raggedright\large
}{}{0em}{}[\color{black}\titlerule \vspace{-5pt}]

%-------------------------
% Custom commands
\newcommand{\resumeItem}[2]{
  \item\small{
    \textbf{#1}{: #2 \vspace{-2pt}}
  }
}

\newcommand{\resumeSubheading}[4]{
  \vspace{10pt}
    \begin{tabular*}{0.97\textwidth}{l@{\extracolsep{\fill}}r}
      \textbf{#1} & #2 \\ 
      \textit{\small#3} & \textit{\small #4} \\
    \end{tabular*}\vspace{-5pt}
}

\newcommand{\resumeSubItem}[2]{\resumeItem{#1}{#2}\vspace{-4pt}}

\renewcommand{\labelitemii}{$\circ$}

\newcommand{\resumeSubHeadingListStart}{\begin{itemize}[leftmargin=*]}
\newcommand{\resumeSubHeadingListEnd}{\end{itemize}}
\newcommand{\resumeItemListStart}{\begin{itemize}}
\newcommand{\resumeItemListEnd}{\end{itemize}\vspace{-5pt}}

%-------------------------------------------
%%%%%%  CV STARTS HERE  %%%%%%%%%%%%%%%%%%%%%%%%%%%%


\begin{document}

%----------HEADING-----------------
\begin{tabular*}{\textwidth}{l@{\extracolsep{\fill}}r}
  \textbf{\href{https://www.linkedin.com/in/jonathan-farland-40096216}{\Large Jon Farland}} & San Francisco Bay Area \\
  \href{mailto:<EMAIL>}{<EMAIL>} & Mobile: ****** 237 8192 \\
  \href{https://www.youtube.com/watch?v=NlwiA0BAVBw}{Snowflake AI Builders Series} - \href{https://www.youtube.com/watch?v=N0gyDVUFPlg}{Foundational Timeseries Models} - 
  \href{https://github.com/h2oai/h2o_genai_training}{GenAI Training Course} \\ %- \href{https://www.youtube.com/watch?v=nYbqJDCZ2wI}{Silicon Angle}
\end{tabular*}

\section{Prospectus}

Passionate and dedicated player-coach applying advanced technical expertise to ignite sustainable revenue growth.


%-----------EXPERIENCE-----------------
\section{Experience}
  %\resumeSubHeadingListStart

  \resumeSubheading
    {Chief Technology Officer, Founder}{San Francisco Bay Area}
    {Pathfinder Analytics}{Feb 2024 - Present}
    \resumeItemListStart
      \resumeItem{Expert Consulting}
        {Developed new business models for SMB clients by adopting Generative AI}
      \resumeItem{AI Engineering}
        {Delivered an Agentic AI platform based on current state-of-the-art open source software, and tailored for each client's business needs}
    \resumeItemListEnd

  \resumeSubheading
    {Director of AI Solutions Engineering}{Mountain View, CA}
    {H2O.ai}{Dec 2021 - Present}
    \resumeItemListStart
      \resumeItem{Business Growth}
        {TensorFlow is an open source software library for numerical computation using data flow graphs; primarily used for training deep learning models.}
      \resumeItem{Leadership}
        {Apache Beam is a unified model for defining both batch and streaming data-parallel processing pipelines, as well as a set of language-specific SDKs for constructing pipelines and runners.}
      \resumeItem{Technical}
        {this is a thing I did}
    \resumeItemListEnd

  \resumeSubheading
    {Senior Data Science Manager}{Oakland, CA}
    {TROVE Predictive Data Science (Acquired by E-Source)}{Oct 2017 - Dec 2021}
    \resumeItemListStart
      \resumeItem{Notifications}
      {this thing}
    \resumeItemListEnd

  \resumeSubheading
    {Senior Technical Consultant, Data Scientist}{Boston, MA}
    {KEMA (Acquired by Det Norske Veritas)}{Aug 2012 - Oct 2017}
    \resumeItemListStart
      \resumeItem{Portfolio Management}
        {Created models for portfolio hedging,  portfolio optimization and price forecasting. Also creating a strategy backtesting engine used for simulating and backtesting strategies.}
      \resumeItem{QuantDesk}
        {Python backend for a web application used by hedge fund managers for portfolio management.}
    \resumeItemListEnd

%\resumeSubHeadingListEnd

%-----------EDUCATION-----------------
\section{Education}
  \resumeSubheading
    {University of Massachusetts}{Amherst, MA}
    {Isenberg School of Management}{Aug 2013}
    \resumeItemListStart
      \resumeItem{Masters of Science}{Applied Econometrics}
      \resumeItem{Graduate Research Focus}{Applying machine learning techniques in heirarchal energy forecasting systems}
      \resumeItem{Bachelors of Business Administration}{Finance and Operations Management, Cum Laude}
    \resumeItemListEnd
  \resumeSubheading
    {University of Naples Federico}{Portici, Italy}
    {Visiting Graduate Study in Advanced Micro-Econometrics}{Aug 2011}


%--------PROGRAMMING SKILLS------------
\section{Technical Strengths}
\textbf{Generative AI}{: PyTorch, HuggingFace, LangChain, Autogen, NVIDIA, Together.ai, AI21, OpenAI, Cohere} \\
\textbf{Predictive AI}{: Sklearn, Autogluon, NeuralProphet, Xgboost, Lightgbm, H2O} \\
\textbf{Languages}{: Python, R, SQL, SAS, Matlab, Mathematica} \\
\textbf{Computing}{: Unix-based Systems, Spark, Docker, Kubernetes, AWS, GCP, Azure} \\
\textbf{Database}{: Postgresql, QuestDB, MongoDB, Cassandra, GBG, ChromaDB}


%-----------PROJECTS-----------------
\section{Projects}
  \resumeSubHeadingListStart
    \resumeSubItem{QuantSoftware Toolkit}
      {Open source python library for financial data analysis and machine learning for finance.}
    \resumeSubItem{Github Visualization}
      {Data Visualization of Git Log data using D3 to analyze project trends over time.}
    \resumeSubItem{Recommendation System}
      {Music and Movie recommender systems using collaborative filtering on public datasets.}
%     \resumeSubItem{Mac Setup}
%       {Book that gives step by step instructions on setting up developer environment on Mac OS.}
  \resumeSubHeadingListEnd



%-------------------------------------------
\end{document}
